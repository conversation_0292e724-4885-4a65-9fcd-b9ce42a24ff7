<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MarkDown Editor</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --bg-color: #ffffff;
      --text-color: #333;
      --border-color: #ddd;
      --hover-color: #f5f5f5;
      --sidebar-width: 200px;
      --header-height: 50px;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      color: var(--text-color);
      background: var(--bg-color);
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }

    header {
      height: var(--header-height);
      background: var(--bg-color);
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      padding: 0 1rem;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .logo {
      font-weight: bold;
      font-size: 1.25rem;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .toolbar {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-left: 1rem;
      flex-grow: 1;
      overflow-x: auto;
      padding: 0.25rem 0;
    }

    .toolbar button {
      background: transparent;
      border: none;
      border-radius: 4px;
      padding: 0.25rem 0.5rem;
      cursor: pointer;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      color: var(--primary-color);
    }

    .toolbar button:hover {
      background: var(--hover-color);
    }

    .toolbar button i {
      font-size: 1rem;
    }

    .toolbar .separator {
      width: 1px;
      height: 1.5rem;
      background: var(--border-color);
      margin: 0 0.25rem;
    }

    .main-container {
      display: flex;
      flex-grow: 1;
      overflow: hidden;
    }

    .sidebar {
      width: var(--sidebar-width);
      border-right: 1px solid var(--border-color);
      overflow-y: auto;
      background: var(--bg-color);
    }

    .file-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .file-list li {
      padding: 0.75rem 1rem;
      cursor: pointer;
      border-bottom: 1px solid var(--border-color);
    }

    .file-list li:hover {
      background: var(--hover-color);
    }

    .file-list .selected {
      background: rgba(52, 152, 219, 0.1);
      border-left: 3px solid var(--secondary-color);
    }

    .editor-container {
      display: flex;
      flex-grow: 1;
      overflow: hidden;
    }

    .editor-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    #editor {
      flex-grow: 1;
      overflow-y: auto;
      padding: 1rem;
      line-height: 1.6;
      font-size: 1rem;
      outline: none;
      white-space: pre-wrap;
    }

    #preview {
      flex: 1;
      overflow-y: auto;
      padding: 1rem 2rem;
      border-left: 1px solid var(--border-color);
    }

    #preview h1, #preview h2, #preview h3, #preview h4, #preview h5, #preview h6 {
      margin-top: 1.5em;
      margin-bottom: 1em;
      line-height: 1.2;
    }

    #preview h1 {
      font-size: 2em;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.3em;
    }

    #preview h2 {
      font-size: 1.5em;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.3em;
    }

    #preview p {
      margin: 1em 0;
    }

    #preview ul, #preview ol {
      padding-left: 2em;
      margin: 1em 0;
    }

    #preview blockquote {
      border-left: 4px solid var(--border-color);
      margin-left: 0;
      padding-left: 1em;
      color: #666;
    }

    #preview pre {
      background: #f6f8fa;
      padding: 1em;
      border-radius: 4px;
      overflow-x: auto;
    }

    #preview code {
      font-family: monospace;
      background: #f6f8fa;
      padding: 0.2em 0.4em;
      border-radius: 3px;
    }

    #preview table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
    }

    #preview th, #preview td {
      border: 1px solid var(--border-color);
      padding: 0.5em;
    }

    #preview img {
      max-width: 100%;
    }

    .toggle-preview {
      position: absolute;
      right: 1rem;
      top: calc(var(--header-height) + 0.5rem);
      background: var(--secondary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0.25rem 0.5rem;
      cursor: pointer;
      z-index: 10;
    }

    .status-bar {
      height: 24px;
      background: #f5f5f5;
      border-top: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      padding: 0 1rem;
      font-size: 0.8rem;
      color: #666;
    }

    .status-bar .statistics {
      margin-left: auto;
    }

    .view-mode-toggle {
      display: flex;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      overflow: hidden;
      margin-left: auto;
    }

    .view-mode-toggle button {
      border: none;
      background: var(--bg-color);
      padding: 0.25rem 0.75rem;
      cursor: pointer;
    }

    .view-mode-toggle button.active {
      background: var(--secondary-color);
      color: white;
    }

    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: var(--bg-color);
      min-width: 160px;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      z-index: 100;
      border-radius: 4px;
    }

    .dropdown-content a {
      color: var(--text-color);
      padding: 12px 16px;
      text-decoration: none;
      display: block;
    }

    .dropdown-content a:hover {
      background-color: var(--hover-color);
    }

    .dropdown:hover .dropdown-content {
      display: block;
    }

    /* Dark mode */
    @media (prefers-color-scheme: dark) {
      :root {
        --primary-color: #ecf0f1;
        --secondary-color: #3498db;
        --bg-color: #1e1e1e;
        --text-color: #f5f5f5;
        --border-color: #333;
        --hover-color: #2a2a2a;
      }

      #preview pre, #preview code {
        background: #282c34;
      }

      .status-bar {
        background: #252525;
        color: #aaa;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">
      <i class="mdi mdi-markdown"></i>
      MarkDown Editor
    </div>
    <div class="toolbar">
      <div class="dropdown">
        <button><i class="mdi mdi-file"></i> File</button>
        <div class="dropdown-content">
          <a href="#" id="new-file"><i class="mdi mdi-file-plus"></i> New</a>
          <a href="#" id="open-file"><i class="mdi mdi-folder-open"></i> Open</a>
          <a href="#" id="save-file"><i class="mdi mdi-content-save"></i> Save</a>
          <a href="#" id="export-html"><i class="mdi mdi-language-html5"></i> Export to HTML</a>
          <a href="#" id="export-pdf"><i class="mdi mdi-file-pdf"></i> Export to PDF</a>
        </div>
      </div>
      <div class="separator"></div>
      <button id="btn-bold" title="Bold"><i class="mdi mdi-format-bold"></i></button>
      <button id="btn-italic" title="Italic"><i class="mdi mdi-format-italic"></i></button>
      <button id="btn-heading" title="Heading"><i class="mdi mdi-format-header-1"></i></button>
      <button id="btn-list" title="List"><i class="mdi mdi-format-list-bulleted"></i></button>
      <button id="btn-ordered-list" title="Ordered List"><i class="mdi mdi-format-list-numbered"></i></button>
      <button id="btn-quote" title="Quote"><i class="mdi mdi-format-quote-open"></i></button>
      <button id="btn-code" title="Code"><i class="mdi mdi-code-tags"></i></button>
      <button id="btn-link" title="Link"><i class="mdi mdi-link"></i></button>
      <button id="btn-image" title="Image"><i class="mdi mdi-image"></i></button>
      <button id="btn-table" title="Table"><i class="mdi mdi-table"></i></button>
      <button id="btn-horizontal-rule" title="Horizontal Rule"><i class="mdi mdi-minus"></i></button>
      <div class="view-mode-toggle">
        <button class="active" id="view-edit">Edit</button>
        <button id="view-split">Split</button>
        <button id="view-preview">Preview</button>
      </div>
    </div>
  </header>

  <div class="main-container">
    <div class="editor-container">
      <div class="editor-wrapper">
        <div id="editor" contenteditable="true" spellcheck="false"></div>
      </div>
      <div id="preview"></div>
    </div>
  </div>

  <div class="status-bar">
    <span id="file-name">Untitled.md</span>
    <div class="statistics">
      <span id="word-count">0 words</span> |
      <span id="char-count">0 characters</span>
    </div>
  </div>

  <input type="file" id="file-input" style="display: none">

  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/core.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/languages/javascript.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/languages/python.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/languages/css.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/languages/xml.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const editor = document.getElementById('editor');
      const preview = document.getElementById('preview');
      const wordCount = document.getElementById('word-count');
      const charCount = document.getElementById('char-count');
      const fileName = document.getElementById('file-name');
      const fileInput = document.getElementById('file-input');
      let currentFileName = 'Untitled.md';

      // Initialize with sample content
      editor.innerHTML = `# Welcome to Markdown Editor

This is a **WYSIWYG** markdown editor that works locally. Try it out!

## Features
- Real-time preview
- Syntax highlighting
- File management
- Export options

> Start typing to see the magic happen!

\`\`\`javascript
function helloWorld() {
  console.log("Hello, world!");
}
\`\`\`

| Feature | Status |
|---------|--------|
| Editing | ✅ |
| Preview | ✅ |
| Export  | ✅ |

![Markdown Logo](https://markdown-here.com/img/icon256.png)
`;

      // Set up marked with highlight.js
      marked.setOptions({
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
          }
          return hljs.highlightAuto(code).value;
        },
        breaks: true
      });

      // Render initial preview
      renderPreview();

      // Event listeners
      editor.addEventListener('input', () => {
        renderPreview();
        updateStatistics();
      });

      // View mode toggles
      document.getElementById('view-edit').addEventListener('click', () => {
        setViewMode('edit');
      });
      
      document.getElementById('view-split').addEventListener('click', () => {
        setViewMode('split');
      });
      
      document.getElementById('view-preview').addEventListener('click', () => {
        setViewMode('preview');
      });

      // Formatting buttons
      document.getElementById('btn-bold').addEventListener('click', () => {
        formatText('**', '**');
      });
      
      document.getElementById('btn-italic').addEventListener('click', () => {
        formatText('*', '*');
      });
      
      document.getElementById('btn-heading').addEventListener('click', () => {
        formatText('# ', '');
      });
      
      document.getElementById('btn-list').addEventListener('click', () => {
        formatText('- ', '');
      });
      
      document.getElementById('btn-ordered-list').addEventListener('click', () => {
        formatText('1. ', '');
      });
      
      document.getElementById('btn-quote').addEventListener('click', () => {
        formatText('> ', '');
      });
      
      document.getElementById('btn-code').addEventListener('click', () => {
        formatText('`', '`');
      });
      
      document.getElementById('btn-link').addEventListener('click', () => {
        const url = prompt('Enter URL:');
        if (url) {
          formatText('[', `](${url})`);
        }
      });
      
      document.getElementById('btn-image').addEventListener('click', () => {
        const url = prompt('Enter image URL:');
        const alt = prompt('Enter image description:');
        if (url) {
          insertAtCursor(`![${alt || 'Image'}](${url})`);
        }
      });
      
      document.getElementById('btn-table').addEventListener('click', () => {
        insertAtCursor(`
| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |
| Cell 3   | Cell 4   |
        `);
      });
      
      document.getElementById('btn-horizontal-rule').addEventListener('click', () => {
        insertAtCursor(`\n---\n`);
      });

      // File operations
      document.getElementById('new-file').addEventListener('click', () => {
        if (confirm('Create new file? Any unsaved changes will be lost.')) {
          editor.innerHTML = '';
          currentFileName = 'Untitled.md';
          fileName.textContent = currentFileName;
          renderPreview();
          updateStatistics();
        }
      });

      document.getElementById('open-file').addEventListener('click', () => {
        fileInput.click();
      });

      fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(e) {
            editor.innerText = e.target.result;
            currentFileName = file.name;
            fileName.textContent = currentFileName;
            renderPreview();
            updateStatistics();
          };
          reader.readAsText(file);
        }
      });

      document.getElementById('save-file').addEventListener('click', () => {
        const content = editor.innerText;
        const blob = new Blob([content], { type: 'text/markdown' });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = currentFileName;
        a.click();
        URL.revokeObjectURL(a.href);
      });

      document.getElementById('export-html').addEventListener('click', () => {
        const html = preview.innerHTML;
        const blob = new Blob([`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <title>${currentFileName}</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; padding: 2em; max-width: 800px; margin: 0 auto; }
              pre { background: #f6f8fa; padding: 1em; border-radius: 5px; overflow-x: auto; }
              code { font-family: monospace; background: #f6f8fa; padding: 0.2em 0.4em; border-radius: 3px; }
              img { max-width: 100%; }
              table { border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; }
              blockquote { border-left: 4px solid #ddd; margin-left: 0; padding-left: 1em; color: #666; }
            </style>
          </head>
          <body>
            ${html}
          </body>
          </html>
        `], { type: 'text/html' });
        
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = currentFileName.replace(/\.md$/, '.html');
        a.click();
        URL.revokeObjectURL(a.href);
      });

      document.getElementById('export-pdf').addEventListener('click', () => {
        alert('PDF export would typically use a library like jsPDF or print-to-PDF functionality. For now, use your browser\'s print function to save as PDF.');
        window.print();
      });

      // Functions
      function renderPreview() {
        const content = editor.innerText;
        preview.innerHTML = marked.parse(content);
      }

      function updateStatistics() {
        const content = editor.innerText;
        const words = content.trim().split(/\s+/).filter(Boolean).length;
        const chars = content.length;
        
        wordCount.textContent = `${words} word${words !== 1 ? 's' : ''}`;
        charCount.textContent = `${chars} character${chars !== 1 ? 's' : ''}`;
      }

      function setViewMode(mode) {
        // Reset active button states
        document.getElementById('view-edit').classList.remove('active');
        document.getElementById('view-split').classList.remove('active');
        document.getElementById('view-preview').classList.remove('active');
        
        // Set new active button
        document.getElementById(`view-${mode}`).classList.add('active');
        
        // Update view
        switch(mode) {
          case 'edit':
            editor.style.display = 'block';
            preview.style.display = 'none';
            break;
          case 'split':
            editor.style.display = 'block';
            preview.style.display = 'block';
            break;
          case 'preview':
            editor.style.display = 'none';
            preview.style.display = 'block';
            break;
        }
      }

      function formatText(prefix, suffix) {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;
        
        const range = selection.getRangeAt(0);
        if (!range.commonAncestorContainer.parentNode.isSameNode(editor) && 
            !editor.contains(range.commonAncestorContainer)) {
          return;
        }
        
        const selectedText = selection.toString();
        const replacement = `${prefix}${selectedText}${suffix}`;
        
        // Insert the formatted text
        document.execCommand('insertText', false, replacement);
        
        // Update preview
        renderPreview();
      }

      function insertAtCursor(text) {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;
        
        const range = selection.getRangeAt(0);
        if (!range.commonAncestorContainer.parentNode.isSameNode(editor) && 
            !editor.contains(range.commonAncestorContainer)) {
          return;
        }
        
        // Insert the text
        document.execCommand('insertText', false, text);
        
        // Update preview
        renderPreview();
      }

      // Initialize statistics
      updateStatistics();
      
      // Set default view mode
      setViewMode('split');
    });
  </script>
</body>
</html>
