/**
 * Toolbar Module
 * Handles toolbar functionality and formatting buttons
 */

class Toolbar {
  constructor() {
    this.buttons = new Map();
    this.currentOpenDropdown = null; // Track currently open dropdown
    this.initialize();
  }

  /**
   * Initialize toolbar
   */
  initialize() {
    this.setupFormattingButtons();
    this.setupViewModeButtons();
    this.setupDropdowns();
    this.setupMobileMenu();
  }

  /**
   * Setup formatting buttons
   */
  setupFormattingButtons() {
    const formattingButtons = [
      {
        id: 'btn-bold',
        action: () => window.editor.formatText('**', '**'),
        tooltip: 'Bold (Ctrl+B)'
      },
      {
        id: 'btn-italic',
        action: () => window.editor.formatText('*', '*'),
        tooltip: 'Italic (Ctrl+I)'
      },
      {
        id: 'btn-heading',
        action: () => this.insertHeading(),
        tooltip: 'Heading (Ctrl+H)'
      },
      {
        id: 'btn-list',
        action: () => window.editor.formatText('- ', ''),
        tooltip: 'Bullet List'
      },
      {
        id: 'btn-ordered-list',
        action: () => this.insertOrderedList(),
        tooltip: 'Numbered List'
      },
      {
        id: 'btn-quote',
        action: () => window.editor.formatText('> ', ''),
        tooltip: 'Quote'
      },
      {
        id: 'btn-code',
        action: () => this.insertCode(),
        tooltip: 'Code'
      },
      {
        id: 'btn-link',
        action: () => window.editor.insertLink(),
        tooltip: 'Link (Ctrl+K)'
      },
      {
        id: 'btn-image',
        action: () => window.editor.insertImage(),
        tooltip: 'Image (Ctrl+Shift+K)'
      },
      {
        id: 'btn-table',
        action: () => this.insertTable(),
        tooltip: 'Table'
      },
      {
        id: 'btn-horizontal-rule',
        action: () => this.insertHorizontalRule(),
        tooltip: 'Horizontal Rule'
      },
      {
        id: 'btn-equation',
        action: () => this.insertEquation(),
        tooltip: 'Math Equation (Ctrl+M)'
      },
      {
        id: 'btn-mermaid',
        action: () => this.insertMermaidDiagram(),
        tooltip: 'Mermaid Diagram'
      }
    ];

    formattingButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', (e) => {
          e.preventDefault();
          button.action();
        });
        
        // Update tooltip
        element.title = button.tooltip;
        element.setAttribute('aria-label', button.tooltip);
        
        this.buttons.set(button.id, {
          element,
          action: button.action,
          tooltip: button.tooltip
        });
      }
    });
  }

  /**
   * Setup view mode buttons
   */
  setupViewModeButtons() {
    const viewModeButtons = [
      { id: 'view-edit', mode: 'edit' },
      { id: 'view-split', mode: 'split' },
      { id: 'view-preview', mode: 'preview' }
    ];

    viewModeButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', (e) => {
          e.preventDefault();
          window.editor.setViewMode(button.mode);
        });
      }
    });
  }

  /**
   * Setup dropdown menus
   */
  setupDropdowns() {
    // File dropdown (only if it exists - removed in Electron version)
    this.setupFileDropdown();

    // Handle dropdown accessibility
    this.setupDropdownAccessibility();
  }

  /**
   * Setup file dropdown (optional - may not exist in Electron version)
   */
  setupFileDropdown() {
    const fileButtons = [
      {
        id: 'new-file',
        action: () => window.fileManager?.newFile()
      },
      {
        id: 'open-file',
        action: () => window.fileManager?.openFile()
      },
      {
        id: 'save-file',
        action: () => window.fileManager?.saveFile()
      },
      {
        id: 'save-as',
        action: () => window.fileManager?.saveAs()
      },
      {
        id: 'export-html',
        action: () => window.fileManager?.exportHtml()
      },
      {
        id: 'export-pdf',
        action: () => window.fileManager?.exportPdf()
      }
    ];

    fileButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', (e) => {
          e.preventDefault();
          button.action();
          this.closeDropdowns();
        });
      }
    });
  }

  /**
   * Setup dropdown accessibility
   */
  setupDropdownAccessibility() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
      const trigger = dropdown.querySelector('.dropdown-trigger');
      const content = dropdown.querySelector('.dropdown-content');
      
      if (trigger && content) {
        // Handle keyboard navigation
        trigger.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.toggleDropdown(dropdown);
          } else if (e.key === 'Escape') {
            this.closeDropdown(dropdown);
          }
        });

        // Handle mouse events
        trigger.addEventListener('click', (e) => {
          e.preventDefault();
          this.toggleDropdown(dropdown);
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
          if (!dropdown.contains(e.target)) {
            this.closeDropdown(dropdown);
          }
        });
      }
    });
  }

  /**
   * Toggle dropdown
   */
  toggleDropdown(dropdown) {
    const isOpen = dropdown.classList.contains('open');
    this.closeDropdowns(); // Close all dropdowns first
    
    if (!isOpen) {
      this.openDropdown(dropdown);
    }
  }

  /**
   * Open dropdown
   */
  openDropdown(dropdown) {
    dropdown.classList.add('open');
    this.currentOpenDropdown = dropdown; // Track the open dropdown
    const trigger = dropdown.querySelector('.dropdown-trigger');
    const content = dropdown.querySelector('.dropdown-content');

    if (trigger && content) {
      trigger.setAttribute('aria-expanded', 'true');
      content.style.display = 'block';

      // Adjust dropdown position to stay within viewport
      this.adjustDropdownPosition(dropdown, content);

      // Focus first menu item
      const firstMenuItem = content.querySelector('button');
      if (firstMenuItem) {
        firstMenuItem.focus();
      }
    }
  }

  /**
   * Adjust dropdown position to stay within viewport
   */
  adjustDropdownPosition(dropdown, content) {
    const trigger = dropdown.querySelector('.dropdown-trigger');
    if (!trigger) return;

    // Get trigger button position
    const triggerRect = trigger.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Calculate initial position (below the trigger)
    let top = triggerRect.bottom + 2; // 2px gap
    let left = triggerRect.left;

    // Get dropdown dimensions (temporarily show it to measure)
    content.style.visibility = 'hidden';
    content.style.display = 'block';
    const contentRect = content.getBoundingClientRect();
    content.style.visibility = '';

    // Adjust horizontal position if it goes off-screen
    if (left + contentRect.width > viewportWidth) {
      left = triggerRect.right - contentRect.width;
    }

    // Adjust vertical position if it goes off-screen
    if (top + contentRect.height > viewportHeight) {
      top = triggerRect.top - contentRect.height - 2; // Show above instead
    }

    // Ensure it doesn't go above the top of the viewport
    if (top < 0) {
      top = 2;
    }

    // Ensure it doesn't go beyond the left edge
    if (left < 0) {
      left = 2;
    }

    // Apply the calculated position
    content.style.top = `${top}px`;
    content.style.left = `${left}px`;
  }

  /**
   * Close dropdown
   */
  closeDropdown(dropdown) {
    dropdown.classList.remove('open');
    if (this.currentOpenDropdown === dropdown) {
      this.currentOpenDropdown = null; // Clear tracking
    }
    const trigger = dropdown.querySelector('.dropdown-trigger');
    const content = dropdown.querySelector('.dropdown-content');

    if (trigger && content) {
      trigger.setAttribute('aria-expanded', 'false');
      content.style.display = 'none';
      // Reset positioning
      content.style.top = '';
      content.style.left = '';
    }
  }

  /**
   * Close all dropdowns
   */
  closeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => this.closeDropdown(dropdown));
  }

  /**
   * Setup mobile menu functionality
   */
  setupMobileMenu() {
    const mobileToggle = document.getElementById('mobile-menu-toggle');
    const toolbar = document.getElementById('toolbar');

    if (mobileToggle && toolbar) {
      mobileToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleMobileMenu();
      });

      // Close mobile menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!mobileToggle.contains(e.target) && !toolbar.contains(e.target)) {
          this.closeMobileMenu();
        }
      });

      // Close mobile menu on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.closeMobileMenu();
        }
      });

      // Close mobile menu when window is resized to larger screen
      window.addEventListener('resize', () => {
        if (window.innerWidth > 480) {
          this.closeMobileMenu();
        }
      });
    }
  }

  /**
   * Toggle mobile menu
   */
  toggleMobileMenu() {
    const toolbar = document.getElementById('toolbar');
    const mobileToggle = document.getElementById('mobile-menu-toggle');

    if (toolbar && mobileToggle) {
      const isOpen = toolbar.classList.contains('mobile-open');

      if (isOpen) {
        this.closeMobileMenu();
      } else {
        this.openMobileMenu();
      }
    }
  }

  /**
   * Open mobile menu
   */
  openMobileMenu() {
    const toolbar = document.getElementById('toolbar');
    const mobileToggle = document.getElementById('mobile-menu-toggle');

    if (toolbar && mobileToggle) {
      toolbar.classList.add('mobile-open');
      mobileToggle.setAttribute('aria-expanded', 'true');

      // Change icon to close
      const icon = mobileToggle.querySelector('i');
      if (icon) {
        icon.className = 'mdi mdi-close';
      }
    }
  }

  /**
   * Close mobile menu
   */
  closeMobileMenu() {
    const toolbar = document.getElementById('toolbar');
    const mobileToggle = document.getElementById('mobile-menu-toggle');

    if (toolbar && mobileToggle) {
      toolbar.classList.remove('mobile-open');
      mobileToggle.setAttribute('aria-expanded', 'false');

      // Change icon back to menu
      const icon = mobileToggle.querySelector('i');
      if (icon) {
        icon.className = 'mdi mdi-menu';
      }

      // Also close any open dropdowns
      this.closeDropdowns();
    }
  }

  /**
   * Insert heading with level selection
   */
  async insertHeading() {
    const selectedText = EditorUtils.getSelectedText(window.editor.editorElement);
    const level = await EditorUtils.showPromptDialog('Enter heading level (1-6):', '1');

    if (level && /^[1-6]$/.test(level)) {
      const hashes = '#'.repeat(parseInt(level));
      const text = selectedText || 'Heading';
      EditorUtils.insertTextAtCursor(window.editor.editorElement, `${hashes} ${text}`);
      window.editor.handleInput();
    }

    window.editor.focus();
  }

  /**
   * Insert ordered list
   */
  insertOrderedList() {
    const selectedText = EditorUtils.getSelectedText(window.editor.editorElement);
    
    if (selectedText) {
      const lines = selectedText.split('\n');
      const numberedLines = lines.map((line, index) => 
        line.trim() ? `${index + 1}. ${line.trim()}` : line
      );
      EditorUtils.insertTextAtCursor(window.editor.editorElement, numberedLines.join('\n'));
    } else {
      EditorUtils.insertTextAtCursor(window.editor.editorElement, '1. ');
    }
    
    window.editor.handleInput();
    window.editor.focus();
  }

  /**
   * Insert code (inline or block)
   */
  async insertCode() {
    const selectedText = EditorUtils.getSelectedText(window.editor.editorElement);

    if (selectedText.includes('\n')) {
      // Multi-line: use code block
      const language = await EditorUtils.showPromptDialog('Enter language (optional):', '');
      EditorUtils.insertTextAtCursor(
        window.editor.editorElement,
        `\`\`\`${language || ''}\n${selectedText}\n\`\`\``
      );
    } else {
      // Single line: use inline code
      window.editor.formatText('`', '`');
      return; // formatText already handles input and focus
    }

    window.editor.handleInput();
    window.editor.focus();
  }

  /**
   * Insert table
   */
  async insertTable() {
    const rows = await EditorUtils.showPromptDialog('Number of rows:', '3');
    const cols = await EditorUtils.showPromptDialog('Number of columns:', '3');
    
    if (rows && cols && /^\d+$/.test(rows) && /^\d+$/.test(cols)) {
      const numRows = parseInt(rows);
      const numCols = parseInt(cols);
      
      let table = '';
      
      // Header row
      const headerCells = Array(numCols).fill('Header').map((text, i) => `${text} ${i + 1}`);
      table += `| ${headerCells.join(' | ')} |\n`;
      
      // Separator row
      const separators = Array(numCols).fill('---');
      table += `| ${separators.join(' | ')} |\n`;
      
      // Data rows
      for (let i = 0; i < numRows - 1; i++) {
        const dataCells = Array(numCols).fill('Cell').map((text, j) => `${text} ${i + 1}-${j + 1}`);
        table += `| ${dataCells.join(' | ')} |\n`;
      }
      
      EditorUtils.insertTextAtCursor(window.editor.editorElement, `\n${table}\n`);
      window.editor.handleInput();
    }
    
    window.editor.focus();
  }

  /**
   * Insert horizontal rule
   */
  insertHorizontalRule() {
    EditorUtils.insertTextAtCursor(window.editor.editorElement, '\n---\n');
    window.editor.handleInput();
    window.editor.focus();
  }

  /**
   * Insert math equation
   */
  insertEquation() {
    if (window.katexIntegration && window.katexIntegration.isAvailable()) {
      // Check if Shift key is held for display mode
      const displayMode = event && event.shiftKey;
      window.katexIntegration.insertEquation(window.editor.editorElement, displayMode);
      window.editor.handleInput();
      window.editor.focus();
    } else {
      // Fallback: insert basic LaTeX syntax
      const equation = '$x^2 + y^2 = z^2$';
      EditorUtils.insertTextAtCursor(window.editor.editorElement, equation);
      window.editor.handleInput();
      window.editor.focus();
    }
  }

  /**
   * Insert Mermaid diagram
   */
  insertMermaidDiagram() {
    if (window.mermaidIntegration && window.mermaidIntegration.isAvailable()) {
      this.showMermaidTemplateDialog();
    } else {
      // Fallback: insert basic Mermaid syntax
      const diagram = '```mermaid\ngraph TD\n    A[Start] --> B[End]\n```\n\n';
      EditorUtils.insertTextAtCursor(window.editor.editorElement, diagram);
      window.editor.handleInput();
      window.editor.focus();
    }
  }

  /**
   * Show Mermaid template selection dialog
   */
  showMermaidTemplateDialog() {
    const templates = window.mermaidIntegration.getTemplates();

    // Create modal dialog
    const modal = document.createElement('div');
    modal.className = 'modal mermaid-template-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Insert Mermaid Diagram</h3>
          <button class="modal-close" aria-label="Close">&times;</button>
        </div>
        <div class="modal-body">
          <p>Choose a diagram template:</p>
          <div class="template-grid">
            ${templates.map(template => `
              <button class="template-button" data-template="${template.name}">
                <div class="template-name">${template.name}</div>
                <div class="template-preview">
                  <pre><code>${template.code.split('\n').slice(0, 3).join('\n')}...</code></pre>
                </div>
              </button>
            `).join('')}
          </div>
          <div class="modal-actions">
            <button class="btn btn-secondary" id="cancel-template">Cancel</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    const closeModal = () => {
      document.body.removeChild(modal);
    };

    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.querySelector('#cancel-template').addEventListener('click', closeModal);

    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });

    // Template selection
    modal.querySelectorAll('.template-button').forEach(button => {
      button.addEventListener('click', () => {
        const templateName = button.dataset.template;
        window.mermaidIntegration.insertTemplate(templateName);
        closeModal();
        window.editor.focus();
      });
    });

    // Show modal
    modal.style.display = 'block';
  }

  /**
   * Enable/disable button
   */
  setButtonEnabled(buttonId, enabled) {
    const button = this.buttons.get(buttonId);
    if (button) {
      button.element.disabled = !enabled;
      button.element.classList.toggle('disabled', !enabled);
    }
  }

  /**
   * Update button state
   */
  updateButtonState(buttonId, state) {
    const button = this.buttons.get(buttonId);
    if (button) {
      button.element.classList.toggle('active', state);
      button.element.setAttribute('aria-pressed', state.toString());
    }
  }

  /**
   * Add custom button
   */
  addCustomButton(config) {
    const { id, icon, tooltip, action, position } = config;
    
    const button = document.createElement('button');
    button.id = id;
    button.innerHTML = `<i class="${icon}" aria-hidden="true"></i>`;
    button.title = tooltip;
    button.setAttribute('aria-label', tooltip);
    
    button.addEventListener('click', (e) => {
      e.preventDefault();
      action();
    });
    
    // Insert button at specified position
    const toolbar = document.querySelector('.toolbar');
    const referenceElement = position ? 
      document.getElementById(position) : 
      toolbar.querySelector('.view-mode-toggle');
    
    if (referenceElement) {
      toolbar.insertBefore(button, referenceElement);
    } else {
      toolbar.appendChild(button);
    }
    
    this.buttons.set(id, {
      element: button,
      action,
      tooltip
    });
    
    return button;
  }

  /**
   * Remove custom button
   */
  removeCustomButton(buttonId) {
    const button = this.buttons.get(buttonId);
    if (button) {
      button.element.remove();
      this.buttons.delete(buttonId);
    }
  }

  /**
   * Get button element
   */
  getButton(buttonId) {
    const button = this.buttons.get(buttonId);
    return button ? button.element : null;
  }

  /**
   * Update toolbar state based on editor content
   */
  updateToolbarState() {
    // This could be extended to show active formatting based on cursor position
    // For now, it's a placeholder for future enhancements
  }
}

// Create global instance
window.toolbar = new Toolbar();
