# Simple Mermaid Test

This is a test file to verify Mermaid diagrams are working properly.

## Basic Flowchart

```mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    C --> E[End]
    D --> F[Fix Issues]
    F --> B
```

## Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Editor
    participant Mermaid
    
    User->>Editor: Types diagram code
    Editor->>Mermaid: Process diagram
    Mermaid-->>Editor: Return SVG
    Editor-->>User: Display diagram
```

If you can see the diagrams above as visual graphics (not code blocks), then Mermaid is working correctly!
