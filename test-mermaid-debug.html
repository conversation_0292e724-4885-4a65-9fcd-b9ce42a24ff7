<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .mermaid-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
            background: white;
        }
    </style>
</head>
<body>
    <h1>Mermaid Debug Test</h1>
    
    <div class="test-section">
        <h2>Library Status</h2>
        <div id="mermaid-status" class="status">Checking...</div>
        <div id="version-info" class="status">Version info...</div>
    </div>

    <div class="test-section">
        <h2>Simple Flowchart Test</h2>
        <div class="mermaid-container">
            <div class="mermaid">
graph TD
    A[Start] --> B{Test}
    B -->|Pass| C[Success]
    B -->|Fail| D[Debug]
    C --> E[End]
    D --> E
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Render Test</h2>
        <div id="manual-render" class="mermaid-container">
            <p>Manual render will appear here...</p>
        </div>
        <button onclick="testManualRender()">Test Manual Render</button>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <pre id="console-output">Console messages will appear here...</pre>
    </div>

    <!-- Load Mermaid -->
    <script src="lib/external/mermaid.min.js"></script>
    
    <script>
        // Capture console messages
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${type.toUpperCase()}] ${message}\n`;
        }
        
        console.log = (...args) => {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };
        
        console.error = (...args) => {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };

        // Test Mermaid
        document.addEventListener('DOMContentLoaded', function() {
            const statusElement = document.getElementById('mermaid-status');
            const versionElement = document.getElementById('version-info');
            
            console.log('Testing Mermaid library...');
            
            if (typeof mermaid !== 'undefined') {
                statusElement.textContent = '✅ Mermaid library loaded successfully';
                statusElement.className = 'status success';
                
                versionElement.textContent = `Version: ${mermaid.version || 'unknown'}`;
                versionElement.className = 'status success';
                
                try {
                    // Initialize Mermaid
                    mermaid.initialize({ 
                        startOnLoad: true,
                        theme: 'default',
                        securityLevel: 'loose'
                    });
                    console.log('Mermaid initialized successfully');
                } catch (error) {
                    console.error('Mermaid initialization failed:', error);
                    statusElement.textContent = '❌ Mermaid initialization failed: ' + error.message;
                    statusElement.className = 'status error';
                }
            } else {
                statusElement.textContent = '❌ Mermaid library not found';
                statusElement.className = 'status error';
                console.error('Mermaid library not loaded');
            }
        });

        async function testManualRender() {
            const container = document.getElementById('manual-render');
            
            if (typeof mermaid === 'undefined') {
                container.innerHTML = '<p style="color: red;">Mermaid not available</p>';
                return;
            }
            
            const diagramCode = `graph LR
    A[Manual] --> B[Render]
    B --> C[Test]`;
            
            try {
                console.log('Testing manual render...');
                
                if (typeof mermaid.render === 'function') {
                    console.log('Using mermaid.render API');
                    const { svg } = await mermaid.render('manual-test', diagramCode);
                    container.innerHTML = svg;
                } else if (typeof mermaid.mermaidAPI !== 'undefined' && typeof mermaid.mermaidAPI.render === 'function') {
                    console.log('Using mermaidAPI.render');
                    mermaid.mermaidAPI.render('manual-test', diagramCode, (svg) => {
                        container.innerHTML = svg;
                    });
                } else {
                    console.log('Using fallback method');
                    container.innerHTML = `<div class="mermaid">${diagramCode}</div>`;
                    await mermaid.init(undefined, container.querySelector('.mermaid'));
                }
                
                console.log('Manual render completed successfully');
            } catch (error) {
                console.error('Manual render failed:', error);
                container.innerHTML = `<p style="color: red;">Render failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
