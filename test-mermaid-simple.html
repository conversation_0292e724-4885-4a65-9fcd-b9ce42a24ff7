<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Mermaid Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .mermaid-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            padding: 20px;
            background: white;
        }
    </style>
</head>
<body>
    <h1>Simple Mermaid Test</h1>
    
    <h2>Test 1: Basic Flowchart</h2>
    <div class="mermaid-container">
        <div class="mermaid">
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    C --> E[End]
    D --> F[Fix Issues]
    F --> B
        </div>
    </div>

    <h2>Test 2: Sequence Diagram</h2>
    <div class="mermaid-container">
        <div class="mermaid">
sequenceDiagram
    participant User
    participant Editor
    participant Mermaid
    
    User->>Editor: Types diagram code
    Editor->>Mermaid: Process diagram
    Mermaid-->>Editor: Return SVG
    Editor-->>User: Display diagram
        </div>
    </div>

    <!-- Load Mermaid -->
    <script src="lib/external/mermaid.min.js"></script>
    
    <script>
        // Initialize Mermaid following best practices
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
        
        console.log('Mermaid initialized with startOnLoad: true');
    </script>
</body>
</html>
