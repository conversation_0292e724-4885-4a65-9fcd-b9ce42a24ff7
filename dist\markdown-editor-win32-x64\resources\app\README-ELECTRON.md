# Markdown Editor - Electron App

This is a standalone Windows application version of the Markdown Editor, built with Electron.

## Features

- **Standalone Application**: Runs locally without internet dependencies
- **Native Windows Integration**: Proper Windows app with taskbar icon and window management
- **Local Dependencies**: All libraries (marked.js, highlight.js, Mermaid, KaTeX, Material Design Icons) are bundled locally
- **File System Access**: Full file system access for opening and saving files
- **Menu Integration**: Native Windows menu bar with keyboard shortcuts

## Setup and Installation

### Prerequisites

- Node.js (version 16 or higher)
- npm (comes with Node.js)

### Installation Steps

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Run in Development Mode**:
   ```bash
   npm start
   ```

3. **Build Windows Executable**:
   ```bash
   npm run build
   ```
   This creates a Windows executable in the `dist/` folder.

4. **Build Windows Installer** (optional):
   ```bash
   npm run build-installer
   ```

## File Structure

```
├── main.js                 # Electron main process
├── preload.js             # Electron preload script (security)
├── package.json           # Node.js dependencies and scripts
├── index.html             # Main application HTML
├── assets/                # App icons and resources
├── lib/
│   ├── external/          # Local copies of external libraries
│   │   ├── marked.min.js
│   │   ├── highlight.min.js
│   │   ├── mermaid.min.js
│   │   ├── github.min.css
│   │   ├── materialdesignicons.min.css
│   │   └── fonts/
│   └── katex/            # KaTeX library (already local)
├── scripts/              # Application JavaScript modules
└── styles/               # Application CSS
```

## Changes Made for Electron

1. **Renamed "Save to Directory" to "Save as"** in the File menu
2. **Downloaded all external dependencies locally**:
   - marked.js (Markdown parser)
   - highlight.js (syntax highlighting)
   - Mermaid (diagram rendering)
   - Material Design Icons (fonts and CSS)
3. **Added Electron configuration**:
   - Main process (`main.js`)
   - Preload script (`preload.js`) for security
   - Package configuration (`package.json`)
4. **Integrated native menu system** with keyboard shortcuts
5. **Updated HTML to use local dependencies** instead of CDN links

## Menu Shortcuts

- **Ctrl+N**: New file
- **Ctrl+O**: Open file
- **Ctrl+S**: Save file
- **Ctrl+Shift+S**: Save as
- **F11**: Toggle fullscreen
- **Ctrl+R**: Reload
- **F12**: Toggle developer tools

## Building for Distribution

The built application will be completely self-contained and can be distributed without requiring users to install Node.js or any other dependencies.

### Output Files

After running `npm run build`, you'll find:
- `dist/markdown-editor-win32-x64/` - Portable application folder
- `dist/markdown-editor-win32-x64/markdown-editor.exe` - Main executable

The entire folder can be copied to any Windows machine and run without installation.

## Security Features

- **Context Isolation**: Renderer process is isolated from Node.js APIs
- **Preload Script**: Secure communication between main and renderer processes
- **No Remote Module**: Enhanced security by disabling remote module access
- **Web Security**: Standard web security features enabled

## Troubleshooting

### Common Issues

1. **App won't start**: Make sure all dependencies are installed with `npm install`
2. **Missing icons**: Ensure the Material Design Icons font files are in `lib/external/fonts/`
3. **Math rendering issues**: Check that KaTeX files are present in `lib/katex/`
4. **Build fails**: Ensure you have the latest version of Node.js and npm

### Development Mode

To debug issues, run in development mode:
```bash
npm start
```
This will open the app with developer tools available (F12).

## License

MIT License - See the main project for license details.
