<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="A modern, feature-rich Markdown editor with real-time preview">
  <title>MarkDown Editor</title>
  
  <!-- External Dependencies (Local) -->
  <link rel="stylesheet" href="lib/external/github.min.css">
  <link rel="stylesheet" href="lib/external/materialdesignicons.min.css">

  <!-- KaTeX for Math Rendering -->
  <link rel="stylesheet" href="lib/katex/katex.min.css">

  <!-- Local Styles -->
  <link rel="stylesheet" href="styles/main.css">
</head>
<body>
  <header class="app-header" role="banner">
    <div class="logo">
      <i class="mdi mdi-markdown" aria-hidden="true"></i>
    </div>

    <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle menu" aria-expanded="false">
      <i class="mdi mdi-menu" aria-hidden="true"></i>
    </button>

    <nav class="toolbar" id="toolbar" role="toolbar" aria-label="Editor toolbar">
      <!-- First row: Style bar (toolbar groups) -->
      <div class="toolbar-row-1">
        <div class="toolbar-group" role="group" aria-label="Text formatting">
          <button id="btn-bold" title="Bold (Ctrl+B)" aria-label="Bold">
            <i class="mdi mdi-format-bold" aria-hidden="true"></i>
          </button>
          <button id="btn-italic" title="Italic (Ctrl+I)" aria-label="Italic">
            <i class="mdi mdi-format-italic" aria-hidden="true"></i>
          </button>
          <button id="btn-heading" title="Heading (Ctrl+H)" aria-label="Heading">
            <i class="mdi mdi-format-header-1" aria-hidden="true"></i>
          </button>
        </div>

        <div class="toolbar-separator" role="separator" aria-hidden="true"></div>

        <div class="toolbar-group" role="group" aria-label="Lists and quotes">
          <button id="btn-list" title="Bullet List" aria-label="Bullet List">
            <i class="mdi mdi-format-list-bulleted" aria-hidden="true"></i>
          </button>
          <button id="btn-ordered-list" title="Numbered List" aria-label="Numbered List">
            <i class="mdi mdi-format-list-numbered" aria-hidden="true"></i>
          </button>
          <button id="btn-quote" title="Quote" aria-label="Quote">
            <i class="mdi mdi-format-quote-open" aria-hidden="true"></i>
          </button>
        </div>

        <div class="toolbar-separator" role="separator" aria-hidden="true"></div>

        <div class="toolbar-group" role="group" aria-label="Insert elements">
          <button id="btn-code" title="Code" aria-label="Code">
            <i class="mdi mdi-code-tags" aria-hidden="true"></i>
          </button>
          <button id="btn-link" title="Link" aria-label="Link">
            <i class="mdi mdi-link" aria-hidden="true"></i>
          </button>
          <button id="btn-image" title="Image" aria-label="Image">
            <i class="mdi mdi-image" aria-hidden="true"></i>
          </button>
          <button id="btn-table" title="Table" aria-label="Table">
            <i class="mdi mdi-table" aria-hidden="true"></i>
          </button>
          <button id="btn-horizontal-rule" title="Horizontal Rule" aria-label="Horizontal Rule">
            <i class="mdi mdi-minus" aria-hidden="true"></i>
          </button>
          <button id="btn-equation" title="Math Equation (Ctrl+M)" aria-label="Math Equation">
            <i class="mdi mdi-function-variant" aria-hidden="true"></i>
          </button>
          <button id="btn-mermaid" title="Mermaid Diagram" aria-label="Mermaid Diagram">
            <i class="mdi mdi-chart-timeline-variant" aria-hidden="true"></i>
          </button>
        </div>
      </div>

      <!-- Second row: Edit/Split/Preview buttons -->
      <div class="view-mode-toggle" role="tablist" aria-label="View mode">
        <button role="tab" class="active" id="view-edit" aria-selected="true">
          <i class="mdi mdi-pencil" aria-hidden="true"></i>
          <span>Edit</span>
        </button>
        <button role="tab" id="view-split" aria-selected="false">
          <i class="mdi mdi-view-split-vertical" aria-hidden="true"></i>
          <span>Split</span>
        </button>
        <button role="tab" id="view-preview" aria-selected="false">
          <i class="mdi mdi-eye" aria-hidden="true"></i>
          <span>Preview</span>
        </button>
      </div>
    </nav>
  </header>

  <main class="main-container">
    <div class="editor-container">
      <section class="editor-wrapper" aria-label="Markdown editor">
        <div 
          id="editor" 
          class="editor"
          contenteditable="true" 
          spellcheck="false"
          role="textbox"
          aria-label="Markdown content editor"
          aria-multiline="true"
        ></div>
      </section>
      
      <section 
        id="preview" 
        class="preview"
        aria-label="Markdown preview"
        role="document"
      ></section>
    </div>
  </main>

  <footer class="status-bar" role="contentinfo">
    <span id="file-name" class="file-name">Untitled.md</span>
    <span id="directory-info" class="directory-info" style="display: none;"></span>
    <div class="statistics">
      <span id="word-count" aria-label="Word count">0 words</span>
      <span class="separator">|</span>
      <span id="char-count" aria-label="Character count">0 characters</span>
    </div>
  </footer>

  <!-- Hidden file inputs -->
  <input type="file" id="file-input" accept=".md,.txt,.markdown" aria-hidden="true">
  <input type="file" id="directory-file-input" webkitdirectory directory multiple aria-hidden="true">

  <!-- Directory selection modal -->
  <div id="directory-modal" class="modal" aria-hidden="true">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Select Directory</h3>
        <button class="modal-close" aria-label="Close">&times;</button>
      </div>
      <div class="modal-body">
        <p>Choose a directory to work with:</p>
        <div class="directory-actions">
          <button id="select-directory-btn" class="btn btn-primary">
            <i class="mdi mdi-folder-open"></i>
            Select Directory
          </button>
        </div>
        <div id="recent-directories" class="recent-list">
          <h4>Recent Directories:</h4>
          <ul id="recent-directories-list"></ul>
        </div>
      </div>
    </div>
  </div>

  <!-- External Scripts (Local) -->
  <script src="lib/external/marked.min.js"></script>
  <script src="lib/external/highlight.min.js"></script>

  <!-- Mermaid for Diagrams -->
  <script src="lib/external/mermaid.min.js"></script>

  <!-- KaTeX Scripts -->
  <script src="lib/katex/katex.min.js"></script>
  <script src="lib/katex/auto-render.min.js"></script>

  <!-- Local Scripts -->
  <script src="scripts/utils.js"></script>
  <script src="scripts/katex-integration.js"></script>
  <script src="scripts/mermaid-integration.js"></script>
  <script src="scripts/markdown-renderer.js"></script>
  <script src="scripts/editor.js"></script>
  <script src="scripts/toolbar.js"></script>
  <script src="scripts/file-manager.js"></script>
  <script src="scripts/app.js"></script>
</body>
</html>