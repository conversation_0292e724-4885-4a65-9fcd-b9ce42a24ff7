<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>JavaScript Fixes Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Editor Class Methods</h2>
        <p>Testing if the missing methods are now available:</p>
        <div id="test1-results"></div>
        <button onclick="testEditorMethods()">Test Editor Methods</button>
    </div>

    <div class="test-section">
        <h2>Test 2: Mermaid Integration</h2>
        <p>Testing Mermaid diagram rendering:</p>
        <div id="test2-results"></div>
        <div class="mermaid">
graph TD
    A[Test] --> B{Working?}
    B -->|Yes| C[Success!]
    B -->|No| D[Debug]
        </div>
        <button onclick="testMermaidIntegration()">Test Mermaid</button>
    </div>

    <div class="test-section">
        <h2>Test 3: View Mode Switching</h2>
        <p>Testing view mode changes without errors:</p>
        <div id="test3-results"></div>
        <button onclick="testViewModes()">Test View Modes</button>
    </div>

    <!-- Load dependencies -->
    <script src="lib/external/mermaid.min.js"></script>
    <script src="scripts/utils.js"></script>
    <script src="scripts/mermaid-integration.js"></script>
    <script src="scripts/editor.js"></script>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });

        function testEditorMethods() {
            const results = document.getElementById('test1-results');
            const tests = [];

            try {
                // Test if editor instance exists
                if (window.editor) {
                    tests.push('✓ Editor instance exists');
                    
                    // Test if setPreviewExpanded method exists
                    if (typeof window.editor.setPreviewExpanded === 'function') {
                        tests.push('✓ setPreviewExpanded method exists');
                        
                        // Test calling the method
                        window.editor.setPreviewExpanded(false);
                        tests.push('✓ setPreviewExpanded(false) called successfully');
                    } else {
                        tests.push('✗ setPreviewExpanded method missing');
                    }
                    
                    // Test if togglePreviewExpanded method exists
                    if (typeof window.editor.togglePreviewExpanded === 'function') {
                        tests.push('✓ togglePreviewExpanded method exists');
                    } else {
                        tests.push('✗ togglePreviewExpanded method missing');
                    }
                    
                    // Test if isPreviewExpanded property exists
                    if ('isPreviewExpanded' in window.editor) {
                        tests.push('✓ isPreviewExpanded property exists');
                    } else {
                        tests.push('✗ isPreviewExpanded property missing');
                    }
                    
                } else {
                    tests.push('✗ Editor instance not found');
                }
            } catch (error) {
                tests.push(`✗ Error: ${error.message}`);
            }

            results.innerHTML = tests.map(test => 
                `<div class="${test.startsWith('✓') ? 'success' : 'error'}">${test}</div>`
            ).join('');
        }

        function testMermaidIntegration() {
            const results = document.getElementById('test2-results');
            const tests = [];

            try {
                // Test if mermaid is available
                if (typeof mermaid !== 'undefined') {
                    tests.push('✓ Mermaid library loaded');
                    
                    // Test if mermaidIntegration exists
                    if (window.mermaidIntegration) {
                        tests.push('✓ MermaidIntegration instance exists');
                        
                        if (window.mermaidIntegration.isAvailable()) {
                            tests.push('✓ Mermaid integration is available');
                        } else {
                            tests.push('✗ Mermaid integration not available');
                        }
                    } else {
                        tests.push('✗ MermaidIntegration instance not found');
                    }
                } else {
                    tests.push('✗ Mermaid library not loaded');
                }
            } catch (error) {
                tests.push(`✗ Error: ${error.message}`);
            }

            results.innerHTML = tests.map(test => 
                `<div class="${test.startsWith('✓') ? 'success' : 'error'}">${test}</div>`
            ).join('');
        }

        function testViewModes() {
            const results = document.getElementById('test3-results');
            const tests = [];

            try {
                if (window.editor && typeof window.editor.setViewMode === 'function') {
                    tests.push('✓ setViewMode method exists');
                    
                    // Test different view modes
                    const modes = ['edit', 'split', 'preview'];
                    for (const mode of modes) {
                        try {
                            window.editor.setViewMode(mode);
                            tests.push(`✓ setViewMode('${mode}') called successfully`);
                        } catch (error) {
                            tests.push(`✗ setViewMode('${mode}') failed: ${error.message}`);
                        }
                    }
                } else {
                    tests.push('✗ setViewMode method not found');
                }
            } catch (error) {
                tests.push(`✗ Error: ${error.message}`);
            }

            results.innerHTML = tests.map(test => 
                `<div class="${test.startsWith('✓') ? 'success' : 'error'}">${test}</div>`
            ).join('');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                testEditorMethods();
                testMermaidIntegration();
                testViewModes();
            }, 1000);
        });
    </script>
</body>
</html>
