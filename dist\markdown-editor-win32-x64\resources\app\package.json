{"name": "markdown-editor", "version": "1.0.0", "description": "A modern, feature-rich Markdown editor with real-time preview", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-packager . markdown-editor --platform=win32 --arch=x64 --out=dist --overwrite --icon=assets/icon.ico", "build-installer": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["markdown", "editor", "preview", "electron"], "author": "Markdown Editor", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-packager": "^17.1.1", "electron-builder": "^24.6.4"}, "build": {"appId": "com.markdowneditor.app", "productName": "Markdown Editor", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!*.md", "!test-*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}