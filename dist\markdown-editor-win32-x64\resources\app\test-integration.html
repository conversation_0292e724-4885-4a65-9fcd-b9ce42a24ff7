<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mermaid Integration Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .test-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    .mermaid {
      border: 1px solid #ccc;
      padding: 10px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <h1>Mermaid Integration Test</h1>
  
  <div class="test-section">
    <h2>Test 1: Basic Mermaid Loading</h2>
    <p>Mermaid loaded: <span id="mermaid-status">Checking...</span></p>
  </div>

  <div class="test-section">
    <h2>Test 2: Simple Flowchart</h2>
    <div class="mermaid">
      graph TD
        A[Start] --> B[End]
    </div>
  </div>

  <div class="test-section">
    <h2>Test 3: Sequence Diagram</h2>
    <div class="mermaid">
      sequenceDiagram
        Alice->>Bob: Hello Bob
        Bob-->>Alice: Hello <PERSON>
    </div>
  </div>

  <!-- Mermaid Script -->
  <script src="https://unpkg.com/mermaid@10/dist/mermaid.min.js"></script>
  
  <script>
    // Test Mermaid loading
    document.addEventListener('DOMContentLoaded', function() {
      const statusElement = document.getElementById('mermaid-status');
      
      if (typeof mermaid !== 'undefined') {
        statusElement.textContent = '✅ Loaded successfully';
        statusElement.style.color = 'green';
        
        // Initialize Mermaid
        mermaid.initialize({ 
          startOnLoad: true,
          theme: 'default'
        });
        
        console.log('Mermaid initialized successfully');
      } else {
        statusElement.textContent = '❌ Failed to load';
        statusElement.style.color = 'red';
        console.error('Mermaid failed to load');
      }
    });
  </script>
</body>
</html>
