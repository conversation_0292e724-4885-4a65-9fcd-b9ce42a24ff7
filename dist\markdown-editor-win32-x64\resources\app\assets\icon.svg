<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3498db;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="256" height="256" rx="32" ry="32" fill="url(#grad1)"/>
  
  <!-- Markdown symbol -->
  <g fill="white" transform="translate(32, 32)">
    <!-- M -->
    <path d="M0 32 L0 160 L24 160 L24 64 L48 128 L72 128 L96 64 L96 160 L120 160 L120 32 L80 32 L60 96 L40 32 Z"/>
    
    <!-- Down arrow -->
    <path d="M144 32 L144 96 L128 96 L160 128 L192 96 L176 96 L176 32 Z"/>
  </g>
  
  <!-- Document lines -->
  <g fill="rgba(255,255,255,0.3)" transform="translate(48, 180)">
    <rect x="0" y="0" width="120" height="4" rx="2"/>
    <rect x="0" y="12" width="160" height="4" rx="2"/>
    <rect x="0" y="24" width="100" height="4" rx="2"/>
    <rect x="0" y="36" width="140" height="4" rx="2"/>
  </g>
</svg>
