/**
 * Utility functions for the Markdown Editor
 */

/**
 * Debounce function to limit the rate of function calls
 * @param {Function} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @param {boolean} immediate - Whether to trigger on the leading edge
 * @returns {Function} The debounced function
 */
function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(this, args);
  };
}

/**
 * Throttle function to limit the rate of function calls
 * @param {Function} func - The function to throttle
 * @param {number} limit - The number of milliseconds to wait between calls
 * @returns {Function} The throttled function
 */
function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Get the current selection range in the editor
 * @param {HTMLElement} editor - The editor element
 * @returns {Range|null} The current selection range or null
 */
function getSelectionRange(editor) {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) return null;
  
  const range = selection.getRangeAt(0);
  if (!editor.contains(range.commonAncestorContainer)) return null;
  
  return range;
}

/**
 * Insert text at the current cursor position
 * @param {HTMLElement} editor - The editor element
 * @param {string} text - The text to insert
 */
function insertTextAtCursor(editor, text) {
  const range = getSelectionRange(editor);
  if (!range) return;
  
  // Use the modern approach if available
  if (document.execCommand) {
    document.execCommand('insertText', false, text);
  } else {
    // Fallback for browsers that don't support execCommand
    range.deleteContents();
    const textNode = document.createTextNode(text);
    range.insertNode(textNode);
    range.setStartAfter(textNode);
    range.setEndAfter(textNode);
    
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
  }
}

/**
 * Get the selected text in the editor
 * @param {HTMLElement} editor - The editor element
 * @returns {string} The selected text
 */
function getSelectedText(editor) {
  const range = getSelectionRange(editor);
  return range ? range.toString() : '';
}

/**
 * Wrap selected text with prefix and suffix
 * @param {HTMLElement} editor - The editor element
 * @param {string} prefix - The prefix to add
 * @param {string} suffix - The suffix to add
 */
function wrapSelectedText(editor, prefix, suffix = '') {
  const selectedText = getSelectedText(editor);
  const wrappedText = `${prefix}${selectedText}${suffix}`;
  insertTextAtCursor(editor, wrappedText);
}

/**
 * Count words in text
 * @param {string} text - The text to count words in
 * @returns {number} The number of words
 */
function countWords(text) {
  if (!text || typeof text !== 'string') return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Count characters in text
 * @param {string} text - The text to count characters in
 * @returns {number} The number of characters
 */
function countCharacters(text) {
  return text ? text.length : 0;
}

/**
 * Download a file with the given content
 * @param {string} content - The file content
 * @param {string} filename - The filename
 * @param {string} mimeType - The MIME type
 */
function downloadFile(content, filename, mimeType = 'text/plain') {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  setTimeout(() => URL.revokeObjectURL(url), 100);
}

/**
 * Read a file as text
 * @param {File} file - The file to read
 * @returns {Promise<string>} Promise that resolves to the file content
 */
function readFileAsText(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}

/**
 * Show a confirmation dialog
 * @param {string} message - The message to show
 * @returns {boolean} True if confirmed, false otherwise
 */
function showConfirmDialog(message) {
  return confirm(message);
}

/**
 * Show a prompt dialog
 * @param {string} message - The message to show
 * @param {string} defaultValue - The default value
 * @returns {Promise<string|null>} The entered value or null if cancelled
 */
function showPromptDialog(message, defaultValue = '') {
  return new Promise((resolve) => {
    // Create modal dialog
    const modal = document.createElement('div');
    modal.className = 'modal prompt-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Input Required</h3>
          <button class="modal-close" aria-label="Close">&times;</button>
        </div>
        <div class="modal-body">
          <p>${message}</p>
          <input type="text" class="prompt-input" value="${defaultValue}" placeholder="Enter value...">
          <div class="modal-actions">
            <button class="btn btn-primary" id="prompt-ok">OK</button>
            <button class="btn btn-secondary" id="prompt-cancel">Cancel</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Get elements
    const input = modal.querySelector('.prompt-input');
    const okBtn = modal.querySelector('#prompt-ok');
    const cancelBtn = modal.querySelector('#prompt-cancel');
    const closeBtn = modal.querySelector('.modal-close');

    // Show modal
    modal.style.display = 'flex';

    // Focus input and select text
    setTimeout(() => {
      input.focus();
      input.select();
    }, 100);

    // Close modal function
    const closeModal = (value = null) => {
      document.body.removeChild(modal);
      resolve(value);
    };

    // Event listeners
    okBtn.addEventListener('click', () => {
      const value = input.value.trim();
      closeModal(value || null);
    });

    cancelBtn.addEventListener('click', () => closeModal(null));
    closeBtn.addEventListener('click', () => closeModal(null));

    // Handle keyboard events
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        const value = input.value.trim();
        closeModal(value || null);
      } else if (e.key === 'Escape') {
        e.preventDefault();
        closeModal(null);
      }
    });

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal(null);
      }
    });
  });
}

/**
 * Add keyboard shortcut listener
 * @param {string} key - The key combination (e.g., 'ctrl+s', 'ctrl+shift+p')
 * @param {Function} callback - The callback function
 */
function addKeyboardShortcut(key, callback) {
  const keys = key.toLowerCase().split('+');
  const modifiers = {
    ctrl: keys.includes('ctrl'),
    shift: keys.includes('shift'),
    alt: keys.includes('alt'),
    meta: keys.includes('meta')
  };
  const mainKey = keys[keys.length - 1];
  
  document.addEventListener('keydown', (e) => {
    if (
      e.ctrlKey === modifiers.ctrl &&
      e.shiftKey === modifiers.shift &&
      e.altKey === modifiers.alt &&
      e.metaKey === modifiers.meta &&
      e.key.toLowerCase() === mainKey
    ) {
      e.preventDefault();
      callback(e);
    }
  });
}

/**
 * Format file size in human readable format
 * @param {number} bytes - The size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Escape HTML characters
 * @param {string} text - The text to escape
 * @returns {string} The escaped text
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Generate a unique ID
 * @returns {string} A unique ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Export functions for use in other modules
window.EditorUtils = {
  debounce,
  throttle,
  getSelectionRange,
  insertTextAtCursor,
  getSelectedText,
  wrapSelectedText,
  countWords,
  countCharacters,
  downloadFile,
  readFileAsText,
  showConfirmDialog,
  showPromptDialog,
  addKeyboardShortcut,
  formatFileSize,
  escapeHtml,
  generateId
};
